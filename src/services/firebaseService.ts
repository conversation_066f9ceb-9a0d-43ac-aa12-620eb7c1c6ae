import { debugLog } from '../config/env';

export interface FirebaseAuthResult {
  firebaseIdToken: string;
}

export class FirebaseService {
  private static instance: FirebaseService;

  public static getInstance(): FirebaseService {
    if (!FirebaseService.instance) {
      FirebaseService.instance = new FirebaseService();
    }
    return FirebaseService.instance;
  }

  async exchangeGoogleTokenForFirebaseToken(googleIdToken: string): Promise<FirebaseAuthResult> {
    try {
      debugLog('Exchanging Google token for Firebase token via REST API');

      // Use Firebase REST API to exchange Google ID token for Firebase token
      const response = await fetch(`https://identitytoolkit.googleapis.com/v1/accounts:signInWithIdp?key=AIzaSyD0-cYCFQQtrDB9VzRcXHtqqFTfgd9JXWI`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          postBody: `id_token=${googleIdToken}&providerId=google.com`,
          requestUri: 'http://localhost',
          returnIdpCredential: true,
          returnSecureToken: true,
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        debugLog('Firebase token exchange error:', errorData);
        throw new Error(`Firebase token exchange failed: ${response.status}`);
      }

      const data = await response.json();

      if (!data.idToken) {
        throw new Error('No Firebase ID token received');
      }

      debugLog('Successfully exchanged Google token for Firebase token');

      return {
        firebaseIdToken: data.idToken,
      };
    } catch (error) {
      debugLog('Firebase token exchange error:', error);
      throw error;
    }
  }
}

export const firebaseService = FirebaseService.getInstance();
