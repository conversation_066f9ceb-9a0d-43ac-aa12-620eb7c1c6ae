{"name": "think-wallet", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.8.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "expo": "~53.0.9", "expo-constants": "~17.1.6", "expo-file-system": "^18.1.10", "expo-image-picker": "^16.1.4", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "firebase": "^11.9.1", "react": "19.0.0", "react-hook-form": "^7.57.0", "react-native": "0.79.2", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-uuid": "^2.0.3", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}